// 全局工具函数
window.PMTExperiment = {
    // 格式化数字
    formatNumber: function(num, decimals = 3) {
        if (typeof num !== 'number') return num;
        return num.toFixed(decimals);
    },
    
    // 验证数字输入
    validateNumber: function(value, min = null, max = null) {
        const num = parseFloat(value);
        if (isNaN(num)) return false;
        if (min !== null && num < min) return false;
        if (max !== null && num > max) return false;
        return true;
    },
    
    // 显示提示消息
    showToast: function(message, type = 'info') {
        const toastContainer = this.getToastContainer();
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show`;
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        toastContainer.appendChild(toast);
        
        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    },
    
    // 获取或创建提示容器
    getToastContainer: function() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1050';
            document.body.appendChild(container);
        }
        return container;
    },
    
    // 本地存储数据
    saveToLocalStorage: function(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (e) {
            console.error('保存到本地存储失败:', e);
            return false;
        }
    },
    
    // 从本地存储读取数据
    loadFromLocalStorage: function(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (e) {
            console.error('从本地存储读取失败:', e);
            return null;
        }
    },
    
    // 导出为CSV格式
    exportToCSV: function(data, filename) {
        if (!data || data.length === 0) {
            this.showToast('没有数据可导出', 'warning');
            return;
        }
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => row[header] || '').join(','))
        ].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    },
    
    // 生成实验报告
    generateReport: function(experimentType, measurements, results) {
        const report = {
            title: this.getExperimentTitle(experimentType),
            date: new Date().toLocaleDateString('zh-CN'),
            time: new Date().toLocaleTimeString('zh-CN'),
            measurements: measurements,
            results: results,
            summary: this.generateSummary(experimentType, results)
        };
        
        return report;
    },
    
    // 获取实验标题
    getExperimentTitle: function(type) {
        const titles = {
            'dark_current': '光电倍增管暗电流测量实验',
            'iv_characteristic': '光电倍增管伏安特性测量实验',
            'linearity': '光电倍增管线性关系测量实验',
            'spectrum': '光电倍增管频谱特性测量实验'
        };
        return titles[type] || '光电倍增管实验';
    },
    
    // 生成实验总结
    generateSummary: function(type, results) {
        const summaries = {
            'dark_current': `本次实验测量了光电倍增管的暗电流特性。平均暗电流为 ${results.average_dark_current} nA，标准偏差为 ${results.standard_deviation} nA。暗电流水平${results.average_dark_current < 50 ? '较低，符合' : '偏高，需要检查'}实验要求。`,
            
            'iv_characteristic': `本次实验测量了光电倍增管的伏安特性。在 ${results.voltage_range} 的电压范围内，电流变化范围为 ${results.current_range}。增益随电压呈${results.gain_slope > 0 ? '正' : '负'}相关变化。`,
            
            'linearity': `本次实验验证了光电倍增管的线性响应特性。线性拟合方程为 y = ${results.slope}x + ${results.intercept}，相关系数为 ${results.correlation_coefficient}，线性度误差为 ${results.linearity_error}%。${results.linearity_error < 5 ? '线性度良好' : '线性度需要改善'}。`,
            
            'spectrum': `本次实验使用不同颜色光源测量了光电倍增管的光谱响应特性。测试颜色包括：${results.colors_tested}。峰值响应波长为 ${results.peak_wavelength} nm，峰值电流为 ${results.peak_current} μA。电流变化范围为 ${results.current_range}，波长范围为 ${results.wavelength_range}。`
        };
        
        return summaries[type] || '实验完成，请查看详细结果。';
    },
    
    // 打印实验报告
    printReport: function(experimentType, measurements, results) {
        const report = this.generateReport(experimentType, measurements, results);
        const printWindow = window.open('', '_blank');
        
        const printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>${report.title}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .info { margin-bottom: 20px; }
                    .data-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    .data-table th { background-color: #f2f2f2; }
                    .results { margin: 20px 0; }
                    .summary { margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-left: 4px solid #007bff; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${report.title}</h1>
                    <p>实验日期：${report.date} ${report.time}</p>
                </div>
                
                <div class="info">
                    <h3>实验数据</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                ${Object.keys(measurements[0] || {}).filter(key => key !== 'id').map(key => `<th>${key}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${measurements.map((m, i) => `
                                <tr>
                                    <td>${i + 1}</td>
                                    ${Object.keys(m).filter(key => key !== 'id').map(key => `<td>${m[key]}</td>`).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="results">
                    <h3>计算结果</h3>
                    ${Object.entries(results).filter(([key]) => key !== 'analysis').map(([key, value]) => `
                        <p><strong>${this.getResultLabel(key)}:</strong> ${value}</p>
                    `).join('')}
                </div>
                
                <div class="summary">
                    <h3>实验总结</h3>
                    <p>${report.summary}</p>
                </div>
            </body>
            </html>
        `;
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    },
    
    // 获取结果标签（与实验页面保持一致）
    getResultLabel: function(key) {
        const labels = {
            'average_dark_current': '平均暗电流 (nA)',
            'standard_deviation': '标准偏差 (nA)',
            'voltage_range': '电压范围',
            'current_range': '电流范围',
            'gain_slope': '增益斜率',
            'slope': '斜率',
            'intercept': '截距',
            'correlation_coefficient': '相关系数',
            'linearity_error': '线性度误差 (%)',
            'peak_wavelength': '峰值波长 (nm)',
            'peak_response': '峰值响应',
            'fwhm': '半峰全宽 (nm)',
            'wavelength_range': '波长范围',
            'measurement_count': '测量点数'
        };
        return labels[key] || key;
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 添加返回顶部按钮
    addBackToTopButton();
    
    // 监听滚动事件
    window.addEventListener('scroll', handleScroll);
});

// 添加返回顶部按钮
function addBackToTopButton() {
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTopBtn.className = 'btn btn-primary position-fixed';
    backToTopBtn.style.cssText = `
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: none;
    `;
    backToTopBtn.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
    document.body.appendChild(backToTopBtn);
    
    window.backToTopBtn = backToTopBtn;
}

// 处理滚动事件
function handleScroll() {
    const backToTopBtn = window.backToTopBtn;
    if (backToTopBtn) {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
