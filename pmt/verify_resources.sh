#!/bin/bash

# 验证本地资源文件
echo "================================================"
echo "验证本地静态资源文件"
echo "================================================"
echo

# 检查目录结构
echo "📁 检查目录结构..."
directories=(
    "static/vendor/bootstrap/css"
    "static/vendor/bootstrap/js"
    "static/vendor/fontawesome/css"
    "static/vendor/fontawesome/webfonts"
    "static/vendor/chartjs"
)

for dir in "${directories[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir - 存在"
    else
        echo "❌ $dir - 不存在"
    fi
done

echo
echo "📄 检查关键文件..."

# 检查文件存在性和大小
files=(
    "static/vendor/bootstrap/css/bootstrap.min.css"
    "static/vendor/bootstrap/js/bootstrap.bundle.min.js"
    "static/vendor/fontawesome/css/all.min.css"
    "static/vendor/chartjs/chart.min.js"
    "static/vendor/fontawesome/webfonts/fa-solid-900.woff2"
    "static/vendor/fontawesome/webfonts/fa-regular-400.woff2"
    "static/vendor/fontawesome/webfonts/fa-brands-400.woff2"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        echo "✅ $file - 存在 ($size)"
    else
        echo "❌ $file - 不存在"
    fi
done

echo
echo "🔍 检查文件内容..."

# 检查Bootstrap CSS
if [ -f "static/vendor/bootstrap/css/bootstrap.min.css" ]; then
    if grep -q "Bootstrap" "static/vendor/bootstrap/css/bootstrap.min.css"; then
        echo "✅ Bootstrap CSS - 内容正确"
    else
        echo "❌ Bootstrap CSS - 内容可能有问题"
    fi
fi

# 检查Chart.js
if [ -f "static/vendor/chartjs/chart.min.js" ]; then
    if grep -q "Chart.js" "static/vendor/chartjs/chart.min.js"; then
        echo "✅ Chart.js - 内容正确"
    else
        echo "❌ Chart.js - 内容可能有问题"
    fi
fi

# 检查Font Awesome CSS
if [ -f "static/vendor/fontawesome/css/all.min.css" ]; then
    if grep -q "Font Awesome" "static/vendor/fontawesome/css/all.min.css"; then
        echo "✅ Font Awesome CSS - 内容正确"
    else
        echo "❌ Font Awesome CSS - 内容可能有问题"
    fi
fi

echo
echo "🌐 测试HTTP访问..."

# 测试HTTP访问
if curl -s http://localhost:20050/ > /dev/null; then
    echo "✅ 应用服务器运行正常"
    
    # 测试静态资源HTTP访问
    resources=(
        "/static/vendor/bootstrap/css/bootstrap.min.css"
        "/static/vendor/bootstrap/js/bootstrap.bundle.min.js"
        "/static/vendor/fontawesome/css/all.min.css"
        "/static/vendor/chartjs/chart.min.js"
    )
    
    for resource in "${resources[@]}"; do
        status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:20050$resource")
        if [ "$status" = "200" ]; then
            echo "✅ $resource - HTTP 200"
        else
            echo "❌ $resource - HTTP $status"
        fi
    done
else
    echo "❌ 应用服务器未运行或无法访问"
fi

echo
echo "📊 资源大小统计..."
if [ -d "static/vendor" ]; then
    echo "总大小: $(du -sh static/vendor | cut -f1)"
    echo "Bootstrap: $(du -sh static/vendor/bootstrap 2>/dev/null | cut -f1 || echo '0')"
    echo "Font Awesome: $(du -sh static/vendor/fontawesome 2>/dev/null | cut -f1 || echo '0')"
    echo "Chart.js: $(du -sh static/vendor/chartjs 2>/dev/null | cut -f1 || echo '0')"
fi

echo
echo "================================================"
echo "资源验证完成"
echo "================================================"
