#!/bin/bash

# 图表问题诊断脚本
echo "================================================"
echo "图表功能诊断"
echo "Chart Functionality Diagnosis"
echo "================================================"
echo

# 1. 检查Chart.js是否正确加载
echo "🔍 检查Chart.js资源..."
chart_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/static/vendor/chartjs/chart.min.js)
if [ "$chart_status" = "200" ]; then
    echo "✅ Chart.js文件可访问 (HTTP $chart_status)"
    
    # 检查文件大小
    chart_size=$(curl -s http://localhost:20050/static/vendor/chartjs/chart.min.js | wc -c)
    echo "   文件大小: $chart_size bytes"
    
    # 检查是否包含Chart关键字
    if curl -s http://localhost:20050/static/vendor/chartjs/chart.min.js | grep -q "Chart"; then
        echo "✅ Chart.js内容正确"
    else
        echo "❌ Chart.js内容可能有问题"
    fi
else
    echo "❌ Chart.js文件无法访问 (HTTP $chart_status)"
fi

echo

# 2. 检查实验页面是否正确引用Chart.js
echo "🔍 检查实验页面Chart.js引用..."
for experiment in "dark_current" "iv_characteristic" "linearity" "spectrum"; do
    page_content=$(curl -s http://localhost:20050/experiment/$experiment)
    if echo "$page_content" | grep -q "chartjs/chart.min.js"; then
        echo "✅ $experiment 页面正确引用Chart.js"
    else
        echo "❌ $experiment 页面未正确引用Chart.js"
    fi
done

echo

# 3. 检查图表测试页面
echo "🔍 检查图表测试页面..."
test_page_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/chart-test)
if [ "$test_page_status" = "200" ]; then
    echo "✅ 图表测试页面可访问"
    echo "   访问地址: http://localhost:20050/chart-test"
else
    echo "❌ 图表测试页面无法访问 (HTTP $test_page_status)"
fi

echo

# 4. 测试API数据格式
echo "🔍 测试API数据格式..."

# 测试暗电流API
echo "  测试暗电流API..."
dark_current_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"experiment_type":"dark_current","measurements":[{"voltage":"0","current":"1.2"},{"voltage":"100","current":"3.5"},{"voltage":"200","current":"8.1"}]}' \
    http://localhost:20050/calculate)

if echo "$dark_current_response" | grep -q "average_dark_current"; then
    echo "✅ 暗电流API响应正常"
    echo "   响应示例: $(echo $dark_current_response | head -c 100)..."
else
    echo "❌ 暗电流API响应异常"
    echo "   响应: $dark_current_response"
fi

echo

# 5. 检查JavaScript控制台错误（模拟）
echo "🔍 检查可能的JavaScript问题..."

# 检查实验页面的JavaScript结构
echo "  检查实验页面JavaScript结构..."
js_content=$(curl -s http://localhost:20050/experiment/dark_current | grep -A 50 -B 50 "prepareChartData")
if echo "$js_content" | grep -q "prepareChartData"; then
    echo "✅ prepareChartData函数存在"
else
    echo "❌ prepareChartData函数缺失"
fi

if echo "$js_content" | grep -q "drawDataChart"; then
    echo "✅ drawDataChart函数存在"
else
    echo "❌ drawDataChart函数缺失"
fi

echo

# 6. 生成测试建议
echo "📋 诊断建议:"
echo "1. 在浏览器中访问: http://localhost:20050/chart-test"
echo "2. 打开浏览器开发者工具 (F12)"
echo "3. 查看Console标签页的错误信息"
echo "4. 点击'测试暗电流图表'按钮"
echo "5. 观察调试信息和图表显示"
echo

echo "🔧 如果图表仍有问题，请检查："
echo "- 浏览器控制台是否有JavaScript错误"
echo "- 数据格式是否正确 (x, y坐标都是数字)"
echo "- Chart.js版本是否兼容"
echo "- 网络请求是否成功"

echo
echo "================================================"
echo "诊断完成"
echo "================================================"
