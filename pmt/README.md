# 光电倍增管实验指导系统

一个基于Flask的光电倍增管（PMT）实验指导Web应用，提供完整的实验原理讲解、实验步骤指导、数据输入和结果分析功能。

## 功能特点

### 🔬 实验类型
- **暗电流测量** - 测量PMT在无光照条件下的暗电流特性
- **伏安特性** - 测量PMT的电压-电流特性曲线
- **线性关系** - 验证输出电流与入射光强的线性关系
- **频谱特性** - 测量PMT对不同波长光的响应特性

### 📱 移动端优化
- 响应式设计，完美适配手机和平板
- 触摸友好的界面元素
- 优化的表格和表单布局
- 快速数据输入功能

### 📊 数据分析
- 实时计算实验结果
- 统计分析（平均值、标准偏差、相关系数等）
- 线性拟合和线性度分析
- 光谱特性分析（峰值波长、半峰全宽）

### 💾 数据管理
- 数据导出（JSON/CSV格式）
- 本地存储支持
- 实验报告生成
- 打印功能

## 安装和运行

### 环境要求
- Python 3.7+
- Flask 2.3+
- NumPy 1.24+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd pmt-experiment
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 运行应用
```bash
python app.py
```

5. 打开浏览器访问 `http://localhost:5000`

## 使用指南

### 1. 查看实验原理
- 访问"原理"页面了解PMT的工作原理
- 学习实验的理论基础和安全注意事项

### 2. 选择实验类型
- 从首页选择要进行的实验类型
- 每种实验都有详细的步骤指导

### 3. 输入实验数据
- 使用快速输入表单添加数据
- 或直接在数据表格中编辑
- 支持批量数据输入

### 4. 计算和分析结果
- 点击"计算结果"按钮进行数据分析
- 查看详细的计算结果和分析报告
- 导出数据和生成实验报告

## 实验说明

### 暗电流测量
**目的**: 评估PMT的本底噪声水平
**参数**: 工作电压、暗电流
**分析**: 平均值、标准偏差、电压依赖性

### 伏安特性测量
**目的**: 确定PMT的最佳工作点
**参数**: 工作电压、阳极电流
**分析**: 增益特性、电压-电流关系

### 线性关系测量
**目的**: 确定PMT的线性动态范围
**参数**: 光强、输出电流
**分析**: 线性拟合、相关系数、线性度误差

### 频谱特性测量
**目的**: 使用不同颜色光源了解PMT的光谱响应特性
**参数**: 光源颜色、波长、阳极电流
**分析**: 峰值波长、峰值电流、电流变化范围、光谱响应特性

## 技术架构

### 后端
- **Flask**: Web框架
- **NumPy**: 数值计算
- **Python**: 数据处理和分析

### 前端
- **Bootstrap 5**: 响应式UI框架
- **Font Awesome**: 图标库
- **JavaScript**: 交互逻辑
- **CSS3**: 样式和动画

### 特性
- RESTful API设计
- JSON数据交换
- 客户端数据验证
- 响应式设计
- 打印样式优化

## API接口

### POST /calculate
计算实验结果

**请求参数**:
```json
{
    "experiment_type": "dark_current|iv_characteristic|linearity|spectrum",
    "measurements": [
        {
            "field1": "value1",
            "field2": "value2"
        }
    ]
}
```

**响应格式**:
```json
{
    "result1": "value1",
    "result2": "value2",
    "analysis": "分析结果文本"
}
```

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 项目讨论区

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持四种基本实验类型
- 移动端优化
- 数据导出功能
