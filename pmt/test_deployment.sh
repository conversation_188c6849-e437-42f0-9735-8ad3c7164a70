#!/bin/bash

# 光电倍增管实验指导系统 Docker 部署测试脚本
# PMT Experiment Guide System Docker Deployment Test Script

echo "================================================"
echo "光电倍增管实验指导系统 Docker 部署测试"
echo "PMT Experiment Guide System Docker Deployment Test"
echo "================================================"
echo

# 测试基本连接
echo "🔍 测试基本连接..."
if curl -f -s http://localhost:20050/ > /dev/null; then
    echo "✅ 基本连接测试通过"
else
    echo "❌ 基本连接测试失败"
    exit 1
fi

# 测试首页
echo "🏠 测试首页..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/)
if [ "$response" = "200" ]; then
    echo "✅ 首页访问正常 (HTTP $response)"
else
    echo "❌ 首页访问失败 (HTTP $response)"
fi

# 测试原理页面
echo "📚 测试原理页面..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/principle)
if [ "$response" = "200" ]; then
    echo "✅ 原理页面访问正常 (HTTP $response)"
else
    echo "❌ 原理页面访问失败 (HTTP $response)"
fi

# 测试实验页面
echo "🧪 测试实验页面..."
experiments=("dark_current" "iv_characteristic" "linearity" "spectrum")
for exp in "${experiments[@]}"; do
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/experiment/$exp)
    if [ "$response" = "200" ]; then
        echo "✅ $exp 实验页面访问正常"
    else
        echo "❌ $exp 实验页面访问失败 (HTTP $response)"
    fi
done

# 测试API接口
echo "🔧 测试API接口..."

# 测试暗电流计算
echo "  测试暗电流计算API..."
api_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"experiment_type":"dark_current","measurements":[{"voltage":"1000","current":"15.2"},{"voltage":"1100","current":"18.7"}]}' \
    http://localhost:20050/calculate)

if echo "$api_response" | grep -q "average_dark_current"; then
    echo "✅ 暗电流计算API正常"
    echo "    响应: $(echo $api_response | jq -r '.analysis' 2>/dev/null || echo $api_response)"
else
    echo "❌ 暗电流计算API失败"
    echo "    响应: $api_response"
fi

# 测试线性关系计算
echo "  测试线性关系计算API..."
api_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"experiment_type":"linearity","measurements":[{"light_intensity":"0.5","output_current":"2.60"},{"light_intensity":"1.0","output_current":"5.20"}]}' \
    http://localhost:20050/calculate)

if echo "$api_response" | grep -q "correlation_coefficient"; then
    echo "✅ 线性关系计算API正常"
else
    echo "❌ 线性关系计算API失败"
    echo "    响应: $api_response"
fi

# 测试静态资源
echo "📁 测试静态资源..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/static/css/style.css)
if [ "$response" = "200" ]; then
    echo "✅ CSS文件访问正常"
else
    echo "❌ CSS文件访问失败 (HTTP $response)"
fi

response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:20050/static/js/main.js)
if [ "$response" = "200" ]; then
    echo "✅ JavaScript文件访问正常"
else
    echo "❌ JavaScript文件访问失败 (HTTP $response)"
fi

# 检查容器状态
echo "📊 检查容器状态..."
container_status=$(docker-compose ps --services --filter "status=running" | wc -l)
if [ "$container_status" -gt 0 ]; then
    echo "✅ 容器运行正常"
    docker-compose ps
else
    echo "❌ 容器未正常运行"
    docker-compose ps
fi

# 检查容器日志
echo "📝 检查容器日志..."
echo "最近的日志输出:"
docker-compose logs --tail=5

# 性能测试
echo "⚡ 简单性能测试..."
echo "发送10个并发请求..."
for i in {1..10}; do
    curl -s http://localhost:20050/ > /dev/null &
done
wait
echo "✅ 并发请求测试完成"

echo
echo "================================================"
echo "🎉 Docker部署测试完成！"
echo
echo "📋 访问信息:"
echo "   应用地址: http://localhost:20050"
echo "   容器名称: pmt-experiment-app"
echo "   网络: pmt_pmt-network"
echo
echo "🛠️ 管理命令:"
echo "   查看日志: docker-compose logs -f"
echo "   停止应用: docker-compose down"
echo "   重启应用: docker-compose restart"
echo "   重新构建: docker-compose build"
echo
echo "📱 移动端测试:"
echo "   在手机浏览器中访问: http://[你的IP地址]:20050"
echo "================================================"
