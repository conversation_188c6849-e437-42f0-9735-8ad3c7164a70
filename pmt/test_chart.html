<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .chart-container { width: 100%; height: 400px; margin: 20px 0; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>图表功能测试</h1>
        <p>测试Chart.js是否正常工作</p>
        
        <button onclick="testChart()">测试图表</button>
        <button onclick="clearChart()">清除图表</button>
        
        <div class="chart-container">
            <canvas id="testChart"></canvas>
        </div>
        
        <div id="status"></div>
    </div>

    <script src="http://localhost:20050/static/vendor/chartjs/chart.min.js"></script>
    <script>
        let chart = null;
        
        function updateStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.innerHTML = `<p style="color: ${isError ? 'red' : 'green'}">${message}</p>`;
        }
        
        function testChart() {
            try {
                if (typeof Chart === 'undefined') {
                    updateStatus('❌ Chart.js 未加载！', true);
                    return;
                }
                
                updateStatus('✅ Chart.js 已加载，正在创建图表...');
                
                const ctx = document.getElementById('testChart').getContext('2d');
                
                // 销毁现有图表
                if (chart) {
                    chart.destroy();
                }
                
                // 创建测试数据
                const data = [
                    {x: 0, y: 0.1},
                    {x: 100, y: 2.5},
                    {x: 200, y: 5.8},
                    {x: 300, y: 12.3},
                    {x: 400, y: 25.6},
                    {x: 500, y: 45.2}
                ];
                
                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: '暗电流测试数据',
                            data: data,
                            borderColor: 'rgb(255, 193, 7)',
                            backgroundColor: 'rgba(255, 193, 7, 0.2)',
                            fill: false,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '暗电流-电压特性曲线测试'
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '工作电压 (V)'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '暗电流 (nA)'
                                }
                            }
                        }
                    }
                });
                
                updateStatus('✅ 图表创建成功！Chart.js 工作正常。');
                
            } catch (error) {
                updateStatus(`❌ 图表创建失败: ${error.message}`, true);
                console.error('Chart creation error:', error);
            }
        }
        
        function clearChart() {
            if (chart) {
                chart.destroy();
                chart = null;
                updateStatus('图表已清除');
            } else {
                updateStatus('没有图表需要清除');
            }
        }
        
        // 页面加载时检查Chart.js
        window.addEventListener('load', function() {
            if (typeof Chart === 'undefined') {
                updateStatus('❌ Chart.js 未加载！请检查网络连接。', true);
            } else {
                updateStatus('✅ Chart.js 已成功加载，版本: ' + Chart.version);
            }
        });
    </script>
</body>
</html>
