# 光电倍增管实验指导系统 Docker 部署指南

## 🐳 Docker 部署概述

本系统已完全容器化，支持使用Docker和Docker Compose进行一键部署。应用运行在**20050端口**，完全适配移动端使用。

### 🔧 本地化资源
- ✅ **无CDN依赖**: 所有外部资源已本地化
- ✅ **Bootstrap 5.1.3**: 本地CSS和JS文件
- ✅ **Font Awesome 6.0.0**: 本地图标和字体文件
- ✅ **Chart.js 3.9.1**: 本地图表库
- ✅ **离线可用**: 完全支持离线环境部署

## 📋 部署要求

### 系统要求
- Docker 20.10+
- Docker Compose 1.29+
- 可用内存: 512MB+
- 可用磁盘: 1GB+

### 端口要求
- **20050**: 应用主端口（HTTP）
- 确保防火墙允许20050端口访问

## 🚀 快速部署

### 方法一：使用部署脚本（推荐）
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

### 方法二：手动部署
```bash
# 1. 构建镜像
docker-compose build

# 2. 启动容器
docker-compose up -d

# 3. 检查状态
docker-compose ps
```

### 方法三：直接使用Docker
```bash
# 构建镜像
docker build -t pmt-experiment .

# 运行容器
docker run -d -p 20050:20050 --name pmt-app pmt-experiment
```

## 📊 部署验证

### 自动测试
```bash
# 运行完整测试套件
./test_deployment.sh
```

### 手动验证
```bash
# 检查容器状态
docker-compose ps

# 测试基本连接
curl http://localhost:20050/

# 测试API接口
curl -X POST -H "Content-Type: application/json" \
  -d '{"experiment_type":"dark_current","measurements":[{"voltage":"1000","current":"15.2"}]}' \
  http://localhost:20050/calculate
```

## 🌐 访问应用

### 本地访问
- **主页**: http://localhost:20050
- **原理页面**: http://localhost:20050/principle
- **实验页面**: http://localhost:20050/experiment/dark_current

### 局域网访问
- **移动端**: http://[服务器IP]:20050
- **其他设备**: http://[服务器IP]:20050

### 获取服务器IP
```bash
# Linux/Mac
hostname -I | awk '{print $1}'

# 或者
ip addr show | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' | cut -d/ -f1
```

## 🛠️ 管理命令

### 基本操作
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps
```

### 日志管理
```bash
# 查看实时日志
docker-compose logs -f

# 查看最近日志
docker-compose logs --tail=50

# 查看特定服务日志
docker-compose logs pmt-experiment
```

### 更新部署
```bash
# 停止并删除容器
docker-compose down

# 重新构建镜像
docker-compose build --no-cache

# 启动新容器
docker-compose up -d
```

## 🔧 配置说明

### 环境变量
在`docker-compose.yml`中可以配置以下环境变量：

```yaml
environment:
  - FLASK_ENV=production          # 运行环境
  - FLASK_HOST=0.0.0.0           # 监听地址
  - FLASK_PORT=20050             # 监听端口
  - FLASK_DEBUG=False            # 调试模式
```

### 端口映射
```yaml
ports:
  - "20050:20050"  # 主机端口:容器端口
```

### 数据持久化
```yaml
volumes:
  - ./data:/app/data  # 可选：持久化数据目录
```

## 📱 移动端优化

### 响应式设计
- 完美适配手机和平板
- 触摸友好的界面元素
- 优化的表格和表单布局

### 移动端测试
1. 确保手机和服务器在同一网络
2. 在手机浏览器中访问: `http://[服务器IP]:20050`
3. 测试各项功能是否正常

## 🔍 故障排除

### 常见问题

#### 1. 容器无法启动
```bash
# 检查端口占用
netstat -tlnp | grep 20050

# 检查Docker服务
systemctl status docker

# 查看详细错误
docker-compose logs
```

#### 2. 无法访问应用
```bash
# 检查防火墙
sudo ufw status
sudo ufw allow 20050

# 检查容器网络
docker network ls
docker network inspect pmt_pmt-network
```

#### 3. 性能问题
```bash
# 检查资源使用
docker stats pmt-experiment-app

# 检查容器健康状态
docker inspect pmt-experiment-app | grep Health -A 10
```

### 日志分析
```bash
# 查看应用错误日志
docker-compose logs | grep ERROR

# 查看访问日志
docker-compose logs | grep "GET\|POST"

# 导出日志到文件
docker-compose logs > pmt-app.log
```

## 🔒 安全配置

### 生产环境建议
1. **使用HTTPS**: 配置反向代理（Nginx/Apache）
2. **防火墙设置**: 只开放必要端口
3. **定期更新**: 保持Docker镜像最新
4. **监控日志**: 设置日志监控和告警

### 反向代理示例（Nginx）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:20050;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📈 监控和维护

### 健康检查
```bash
# 检查应用健康状态
curl -f http://localhost:20050/ || echo "应用异常"

# 检查容器健康状态
docker inspect pmt-experiment-app --format='{{.State.Health.Status}}'
```

### 备份和恢复
```bash
# 备份容器数据
docker cp pmt-experiment-app:/app/data ./backup/

# 备份镜像
docker save pmt_pmt-experiment > pmt-experiment-backup.tar

# 恢复镜像
docker load < pmt-experiment-backup.tar
```

## 🎯 部署成功标志

✅ **所有测试通过**
- 基本连接测试: HTTP 200
- 所有页面访问正常
- API接口响应正确
- 静态资源加载正常

✅ **容器状态健康**
- 容器状态: Up
- 健康检查: healthy
- 端口映射: 0.0.0.0:20050->20050/tcp

✅ **功能完整可用**
- 四种实验类型全部可用
- 数据计算功能正常
- 移动端访问流畅

## 📞 技术支持

如遇部署问题，请提供以下信息：
1. 系统环境信息
2. Docker版本信息
3. 错误日志内容
4. 容器状态信息

---

**部署完成后，您的光电倍增管实验指导系统将在 http://localhost:20050 上运行！**
