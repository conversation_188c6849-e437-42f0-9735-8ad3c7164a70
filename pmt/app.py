from flask import Flask, render_template, request, jsonify
import numpy as np
import json
import os
from datetime import datetime
from config import config

# 创建Flask应用
app = Flask(__name__)

# 加载配置
config_name = os.environ.get('FLASK_CONFIG', 'default')
app.config.from_object(config[config_name])

@app.route('/')
def index():
    return render_template('index.html', experiments=app.config['EXPERIMENTS'])

@app.route('/principle')
def principle():
    return render_template('principle.html')

@app.route('/chart-test')
def chart_test():
    return render_template('chart_test.html')

@app.route('/experiment/<experiment_type>')
def experiment(experiment_type):
    experiments = app.config['EXPERIMENTS']

    if experiment_type not in experiments:
        return "实验类型不存在", 404

    return render_template('experiment.html',
                         experiment=experiments[experiment_type],
                         experiment_type=experiment_type)

@app.route('/calculate', methods=['POST'])
def calculate():
    data = request.json
    experiment_type = data.get('experiment_type')
    measurements = data.get('measurements', [])
    
    if not measurements:
        return jsonify({'error': '没有测量数据'}), 400
    
    results = {}
    
    if experiment_type == 'dark_current':
        results = calculate_dark_current(measurements)
    elif experiment_type == 'iv_characteristic':
        results = calculate_iv_characteristic(measurements)
    elif experiment_type == 'linearity':
        results = calculate_linearity(measurements)
    elif experiment_type == 'spectrum':
        results = calculate_spectrum(measurements)
    else:
        return jsonify({'error': '未知的实验类型'}), 400
    
    return jsonify(results)

def calculate_dark_current(measurements):
    voltages = [float(m['voltage']) for m in measurements]
    currents = [float(m['current']) for m in measurements]
    
    avg_current = np.mean(currents)
    std_current = np.std(currents)
    
    return {
        'average_dark_current': round(avg_current, 3),
        'standard_deviation': round(std_current, 3),
        'voltage_range': f"{min(voltages)} - {max(voltages)} V",
        'measurement_count': len(measurements),
        'analysis': f"平均暗电流为 {avg_current:.3f} nA，标准偏差为 {std_current:.3f} nA"
    }

def calculate_iv_characteristic(measurements):
    voltages = np.array([float(m['voltage']) for m in measurements])
    currents = np.array([float(m['current']) for m in measurements])
    
    # 计算增益
    if len(voltages) >= 2:
        # 简单的线性拟合来估算增益变化
        log_currents = np.log10(currents + 1e-10)  # 避免log(0)
        slope = np.polyfit(voltages, log_currents, 1)[0]
        
        return {
            'voltage_range': f"{min(voltages)} - {max(voltages)} V",
            'current_range': f"{min(currents):.2f} - {max(currents):.2f} μA",
            'gain_slope': round(slope, 4),
            'measurement_count': len(measurements),
            'analysis': f"电压范围: {min(voltages)}-{max(voltages)}V，电流变化斜率: {slope:.4f}"
        }
    
    return {'error': '数据点不足，无法计算特性'}

def calculate_linearity(measurements):
    intensities = np.array([float(m['light_intensity']) for m in measurements])
    currents = np.array([float(m['output_current']) for m in measurements])
    
    if len(intensities) >= 2:
        # 线性拟合
        slope, intercept = np.polyfit(intensities, currents, 1)
        correlation = np.corrcoef(intensities, currents)[0, 1]
        
        # 计算线性度
        fitted_currents = slope * intensities + intercept
        linearity_error = np.max(np.abs(currents - fitted_currents)) / np.max(currents) * 100
        
        return {
            'slope': round(slope, 4),
            'intercept': round(intercept, 4),
            'correlation_coefficient': round(correlation, 4),
            'linearity_error': round(linearity_error, 2),
            'measurement_count': len(measurements),
            'analysis': f"线性拟合: y = {slope:.4f}x + {intercept:.4f}，相关系数: {correlation:.4f}，线性度误差: {linearity_error:.2f}%"
        }
    
    return {'error': '数据点不足，无法计算线性关系'}

def calculate_spectrum(measurements):
    wavelengths = np.array([float(m['wavelength']) for m in measurements])
    currents = np.array([float(m['current']) for m in measurements])

    max_current_idx = np.argmax(currents)
    peak_wavelength = wavelengths[max_current_idx]
    peak_current = currents[max_current_idx]

    # 计算光谱响应范围
    min_current = np.min(currents)
    max_current = np.max(currents)
    current_range = max_current - min_current

    # 计算相对响应度（归一化）
    if max_current > 0:
        relative_responses = currents / max_current
        avg_relative_response = np.mean(relative_responses)
    else:
        avg_relative_response = 0

    # 分析光谱特性
    colors = []
    for m in measurements:
        if 'color' in m:
            colors.append(m['color'])

    return {
        'peak_wavelength': int(peak_wavelength),
        'peak_current': round(peak_current, 3),
        'current_range': f"{min_current:.2f} - {max_current:.2f} μA",
        'wavelength_range': f"{min(wavelengths):.0f} - {max(wavelengths):.0f} nm",
        'avg_relative_response': round(avg_relative_response, 3),
        'measurement_count': len(measurements),
        'colors_tested': ', '.join(set(colors)) if colors else '未记录',
        'analysis': f"峰值响应波长: {peak_wavelength:.0f} nm，峰值电流: {peak_current:.3f} μA，电流变化范围: {current_range:.2f} μA"
    }

if __name__ == '__main__':
    app.run(
        debug=app.config['DEBUG'],
        host=app.config['HOST'],
        port=app.config['PORT']
    )
