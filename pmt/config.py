"""
光电倍增管实验指导系统配置文件
PMT Experiment Guide System Configuration
"""

import os

class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'pmt-experiment-secret-key-2024'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # 服务器配置
    HOST = os.environ.get('FLASK_HOST', '0.0.0.0')
    PORT = int(os.environ.get('FLASK_PORT', 20050))
    
    # 应用配置
    APP_NAME = "光电倍增管实验指导系统"
    APP_VERSION = "1.0.0"
    
    # 实验配置
    EXPERIMENTS = {
        'dark_current': {
            'title': '暗电流测量',
            'description': '测量光电倍增管在无光照条件下的暗电流',
            'icon': 'fas fa-moon',
            'color': 'warning',
            'fields': [
                {
                    'name': 'voltage',
                    'label': '工作电压 (V)',
                    'type': 'number',
                    'step': '10',
                    'min': '0',
                    'max': '3000',
                    'placeholder': '例如: 1000'
                },
                {
                    'name': 'current',
                    'label': '暗电流 (nA)',
                    'type': 'number',
                    'step': '0.01',
                    'min': '0',
                    'placeholder': '例如: 15.2'
                }
            ],
            'units': {
                'voltage': 'V',
                'current': 'nA'
            },
            'typical_ranges': {
                'voltage': [0, 2000],
                'current': [0.1, 100]
            }
        },
        
        'iv_characteristic': {
            'title': '伏安特性测量',
            'description': '测量光电倍增管的电压-电流特性曲线',
            'icon': 'fas fa-chart-line',
            'color': 'success',
            'fields': [
                {
                    'name': 'voltage',
                    'label': '工作电压 (V)',
                    'type': 'number',
                    'step': '10',
                    'min': '0',
                    'max': '3000',
                    'placeholder': '例如: 1200'
                },
                {
                    'name': 'current',
                    'label': '阳极电流 (μA)',
                    'type': 'number',
                    'step': '0.1',
                    'min': '0',
                    'placeholder': '例如: 14.2'
                }
            ],
            'units': {
                'voltage': 'V',
                'current': 'μA'
            },
            'typical_ranges': {
                'voltage': [0, 1500],
                'current': [0.1, 100]
            }
        },
        
        'linearity': {
            'title': '线性关系测量',
            'description': '测量光电倍增管输出电流与入射光强的线性关系',
            'icon': 'fas fa-ruler',
            'color': 'info',
            'fields': [
                {
                    'name': 'light_intensity',
                    'label': '光强 (相对单位)',
                    'type': 'number',
                    'step': '0.1',
                    'min': '0',
                    'placeholder': '例如: 0.5'
                },
                {
                    'name': 'output_current',
                    'label': '输出电流 (μA)',
                    'type': 'number',
                    'step': '0.01',
                    'min': '0',
                    'placeholder': '例如: 2.60'
                }
            ],
            'units': {
                'light_intensity': '相对单位',
                'output_current': 'μA'
            },
            'typical_ranges': {
                'light_intensity': [0.1, 10],
                'output_current': [0.1, 50]
            }
        },
        
        'spectrum': {
            'title': '频谱特性测量',
            'description': '使用不同颜色光源测量光电倍增管的光谱响应特性',
            'icon': 'fas fa-rainbow',
            'color': 'danger',
            'fields': [
                {
                    'name': 'color',
                    'label': '光源颜色',
                    'type': 'select',
                    'options': [
                        {'value': 'red', 'text': '红色', 'wavelength': 650},
                        {'value': 'orange', 'text': '橙色', 'wavelength': 590},
                        {'value': 'yellow', 'text': '黄色', 'wavelength': 570},
                        {'value': 'green', 'text': '绿色', 'wavelength': 520},
                        {'value': 'blue', 'text': '蓝色', 'wavelength': 470},
                        {'value': 'violet', 'text': '紫色', 'wavelength': 420}
                    ],
                    'placeholder': '选择光源颜色'
                },
                {
                    'name': 'wavelength',
                    'label': '波长 (nm)',
                    'type': 'number',
                    'step': '1',
                    'min': '200',
                    'max': '1000',
                    'readonly': True,
                    'placeholder': '自动填入'
                },
                {
                    'name': 'current',
                    'label': '阳极电流 (μA)',
                    'type': 'number',
                    'step': '0.01',
                    'min': '0',
                    'placeholder': '例如: 15.2'
                }
            ],
            'units': {
                'wavelength': 'nm',
                'current': 'μA'
            },
            'typical_ranges': {
                'wavelength': [400, 700],
                'current': [0, 100]
            },
            'color_wavelengths': {
                'red': 650,
                'orange': 590,
                'yellow': 570,
                'green': 520,
                'blue': 470,
                'violet': 420
            }
        }
    }
    
    # 计算配置
    CALCULATION_SETTINGS = {
        'precision': 4,  # 计算精度
        'confidence_level': 0.95,  # 置信水平
        'outlier_threshold': 3.0,  # 异常值阈值（标准偏差倍数）
    }
    
    # 数据验证配置
    VALIDATION_RULES = {
        'min_data_points': 3,  # 最少数据点数
        'max_data_points': 1000,  # 最多数据点数
        'required_fields': True,  # 是否要求所有字段必填
    }
    
    # 导出配置
    EXPORT_SETTINGS = {
        'formats': ['json', 'csv'],  # 支持的导出格式
        'include_metadata': True,  # 是否包含元数据
        'timestamp_format': '%Y-%m-%d_%H-%M-%S',  # 时间戳格式
    }
    
    # 安全配置
    SECURITY_SETTINGS = {
        'max_file_size': 10 * 1024 * 1024,  # 最大文件大小 (10MB)
        'allowed_extensions': ['.json', '.csv', '.txt'],  # 允许的文件扩展名
        'rate_limit': 100,  # 每分钟最大请求数
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-change-this'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
