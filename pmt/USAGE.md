# 光电倍增管实验指导系统使用说明

## 快速开始

### 方法一：直接运行（推荐）
如果系统已安装Flask和NumPy：
```bash
python3 app.py
```

### 方法二：使用启动脚本
```bash
# Linux/Mac
./run.sh

# Windows
run.bat

# 或使用Python脚本
python3 run.py
```

### 方法三：手动安装依赖
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 运行应用
python app.py
```

## 访问应用

启动成功后，在浏览器中访问：
- 本地访问：http://localhost:5000
- 局域网访问：http://[你的IP地址]:5000

## 功能使用

### 1. 查看实验原理
- 点击首页的"查看原理"按钮
- 了解PMT的工作原理和实验基础

### 2. 进行实验
选择实验类型：
- **暗电流测量**：测量无光照条件下的暗电流
- **伏安特性**：测量电压-电流特性曲线
- **线性关系**：验证光强与输出电流的线性关系
- **频谱特性**：测量不同波长的响应特性

### 3. 数据输入
两种输入方式：
- **快速输入**：使用右侧表单快速添加数据
- **表格编辑**：直接在数据表格中编辑

### 4. 结果分析
- 点击"计算结果"按钮进行数据分析
- 查看统计结果和分析报告
- 导出数据和生成报告

## 实验数据示例

系统提供了演示数据文件 `demo_data.json`，包含四种实验的示例数据，可以参考数据格式。

### 暗电流测量数据格式
```json
{
  "voltage": "1000",  // 工作电压(V)
  "current": "18.7"   // 暗电流(nA)
}
```

### 伏安特性数据格式
```json
{
  "voltage": "1200",  // 工作电压(V)
  "current": "14.2"   // 阳极电流(μA)
}
```

### 线性关系数据格式
```json
{
  "light_intensity": "0.5",  // 光强(相对单位)
  "output_current": "2.60"   // 输出电流(μA)
}
```

### 频谱特性数据格式
```json
{
  "wavelength": "440",  // 波长(nm)
  "response": "1.00"    // 相对响应度
}
```

## 移动端使用

系统针对移动设备进行了优化：
- 响应式布局，适配各种屏幕尺寸
- 触摸友好的界面元素
- 优化的表格和表单布局
- 快速数据输入功能

## 数据管理

### 导出数据
- **JSON格式**：完整的实验数据和元信息
- **CSV格式**：适合Excel等软件处理
- **实验报告**：包含分析结果的完整报告

### 本地存储
- 浏览器会自动保存输入的数据
- 刷新页面不会丢失数据
- 可以随时清空或重置数据

## 计算功能

### 暗电流分析
- 平均暗电流值
- 标准偏差
- 电压依赖性分析

### 伏安特性分析
- 增益特性计算
- 电压-电流关系拟合
- 最佳工作点推荐

### 线性关系分析
- 线性拟合方程
- 相关系数计算
- 线性度误差评估

### 频谱特性分析
- 峰值波长识别
- 峰值响应度
- 半峰全宽(FWHM)计算
- 光谱响应范围

## 安全注意事项

⚠️ **高压安全**
- PMT工作电压可达2000V
- 操作前确保电源关闭
- 使用绝缘工具
- 避免触摸高压部分

⚠️ **光照保护**
- 避免强光直射PMT
- 在暗室环境下操作
- 使用适当的光阑控制光强

## 故障排除

### 应用无法启动
1. 检查Python版本（需要3.7+）
2. 确保Flask和NumPy已安装
3. 检查端口5000是否被占用

### 计算结果异常
1. 检查输入数据格式
2. 确保所有必填字段已填写
3. 验证数值范围的合理性

### 页面显示异常
1. 清除浏览器缓存
2. 检查网络连接
3. 尝试刷新页面

## 技术支持

如遇问题，请检查：
1. 浏览器控制台错误信息
2. Flask应用日志输出
3. 网络连接状态

## 更新日志

### v1.0.0
- 初始版本发布
- 支持四种基本实验类型
- 移动端优化
- 数据导出功能
- 实验报告生成
