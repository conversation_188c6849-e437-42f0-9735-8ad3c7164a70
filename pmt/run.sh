#!/bin/bash

# 光电倍增管实验指导系统启动脚本
# PMT Experiment Guide System Launcher

echo "================================================"
echo "光电倍增管实验指导系统"
echo "PMT Experiment Guide System"
echo "================================================"
echo

# 检查Python版本
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo "错误: 未找到Python，请先安装Python 3.7或更高版本"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    REQUIRED_VERSION="3.7"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        echo "错误: 需要Python 3.7或更高版本，当前版本: $PYTHON_VERSION"
        exit 1
    fi
    
    echo "Python版本检查通过: $PYTHON_VERSION"
}

# 安装依赖包
install_requirements() {
    if [ -f "requirements.txt" ]; then
        echo "正在检查并安装依赖包..."
        $PYTHON_CMD -m pip install -r requirements.txt
        if [ $? -ne 0 ]; then
            echo "依赖包安装失败，请检查网络连接"
            exit 1
        fi
        echo "依赖包安装完成!"
    fi
}

# 启动应用
start_app() {
    echo
    echo "正在启动Flask应用..."
    echo "服务器地址: http://localhost:5000"
    echo "按 Ctrl+C 停止服务器"
    echo "------------------------------------------------"
    echo
    
    # 在后台延迟打开浏览器
    (sleep 3 && command -v xdg-open &> /dev/null && xdg-open http://localhost:5000) &
    (sleep 3 && command -v open &> /dev/null && open http://localhost:5000) &
    
    # 启动Flask应用
    $PYTHON_CMD run.py
}

# 主函数
main() {
    check_python
    install_requirements
    start_app
}

# 设置脚本可执行权限
chmod +x "$0"

# 运行主函数
main
