<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验原理 - 金属电子逸出功实验</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- MathJax -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 1rem auto;
            padding: 0;
            overflow: hidden;
            backdrop-filter: blur(10px);
            max-width: 100%;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 1rem;
            text-align: center;
        }
        .content-section {
            padding: 2rem 1rem;
        }
        .theory-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
        .equation-box {
            background: linear-gradient(135deg, #e8f4f8, #f0f8ff);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #4ca2cd;
            text-align: center;
        }
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #e17055;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: #6c757d;
        }
        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .diagram-container {
            text-align: center;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            overflow-x: auto;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .main-container {
                margin: 0.5rem;
                border-radius: 15px;
            }
            .hero-section {
                padding: 1.5rem 1rem;
            }
            .hero-section h1 {
                font-size: 1.5rem;
            }
            .content-section {
                padding: 1.5rem 1rem;
            }
            .theory-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .equation-box {
                padding: 1rem;
                margin: 1rem 0;
                font-size: 0.9rem;
            }
            .highlight-box {
                padding: 1.5rem;
                margin: 1.5rem 0;
            }
            .btn-custom {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
                margin: 0.25rem;
            }
            .step-indicator {
                margin: 1rem 0;
            }
            .step {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
                margin: 0 0.25rem;
            }
            .diagram-container {
                padding: 1rem;
                font-size: 0.8rem;
            }
            .diagram-container pre {
                font-size: 0.7rem;
                overflow-x: auto;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .nav-link {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .hero-section h1 {
                font-size: 1.3rem;
            }
            .theory-card {
                padding: 1rem;
            }
            .equation-box {
                padding: 0.75rem;
                font-size: 0.8rem;
            }
            .btn-custom {
                padding: 0.6rem 1.2rem;
                font-size: 0.8rem;
            }
            .step {
                width: 25px;
                height: 25px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container-fluid px-3">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-atom me-2"></i>电子逸出功实验
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">首页</a>
                    <a class="nav-link active" href="{{ url_for('theory') }}">实验原理</a>
                    <a class="nav-link" href="{{ url_for('steps') }}">实验步骤</a>
                    <a class="nav-link" href="{{ url_for('input_data', step=1) }}">开始实验</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 76px;">
        <div class="main-container">
            <!-- Hero Section -->
            <div class="hero-section">
                <h1><i class="fas fa-atom me-3"></i>实验原理</h1>
                <p class="lead">热电子发射与Richardson-Dushman方程</p>
                <div class="step-indicator">
                    <div class="step">1</div>
                    <div class="step active">2</div>
                    <div class="step">3</div>
                    <div class="step">4</div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <div class="row">
                    <div class="col-lg-10 mx-auto">
                        
                        <!-- 热电子发射原理 -->
                        <div class="theory-card">
                            <h2><i class="fas fa-fire me-2"></i>热电子发射原理</h2>
                            <p>
                                热电子发射是指金属在高温下电子获得足够的热能，克服金属表面的势垒而逸出的现象。
                                当金属被加热到足够高的温度时，金属内部的自由电子获得热能，其中一部分电子的动能
                                超过了金属的逸出功，从而能够逸出金属表面。
                            </p>
                            
                            <div class="diagram-container">
                                <h5>能量示意图</h5>
                                <div style="font-family: monospace; line-height: 2;">
                                    <pre>
    真空能级 ─────────────────────────────
                    ↑
                    │ φ (逸出功)
                    ↓
    费米能级 ─────────────────────────────
                    │
                    │ 电子
                    │
    ─────────────────────────────────────
                金属内部
                    </pre>
                                </div>
                                <p class="text-muted">金属中电子需要克服逸出功φ才能逸出到真空中</p>
                            </div>
                        </div>

                        <!-- Richardson-Dushman方程 -->
                        <div class="theory-card">
                            <h2><i class="fas fa-calculator me-2"></i>Richardson-Dushman方程</h2>
                            <p>
                                Richardson-Dushman方程描述了热电子发射电流密度与温度的关系，是热电子发射理论的核心方程：
                            </p>
                            
                            <div class="equation-box">
                                <h4>Richardson-Dushman方程</h4>
                                $$j = AT^2 e^{-\frac{\phi}{kT}}$$
                                <p class="mt-3 mb-0">
                                    其中：$j$ - 电流密度 (A/m²)，$A$ - Richardson常数，$T$ - 绝对温度 (K)，
                                    $\phi$ - 电子逸出功 (eV)，$k$ - 玻尔兹曼常数
                                </p>
                            </div>

                            <p>
                                对方程两边取对数，得到：
                            </p>
                            
                            <div class="equation-box">
                                $$\ln j = \ln A + 2\ln T - \frac{\phi}{kT}$$
                                <p class="mt-2 mb-0">或者</p>
                                $$\ln\left(\frac{j}{T^2}\right) = \ln A - \frac{\phi}{kT}$$
                            </div>
                        </div>

                        <!-- Schottky效应 -->
                        <div class="theory-card">
                            <h2><i class="fas fa-bolt me-2"></i>Schottky效应</h2>
                            <p>
                                当在阴极和阳极之间施加电压时，会产生电场，这个电场会降低电子的逸出功，
                                这种现象称为Schottky效应。考虑Schottky效应后，电流密度方程变为：
                            </p>
                            
                            <div class="equation-box">
                                $$j = AT^2 e^{-\frac{\phi - \Delta\phi}{kT}}$$
                                <p class="mt-3 mb-0">
                                    其中 $\Delta\phi = \sqrt{\frac{e^3E}{4\pi\epsilon_0}}$ 是逸出功的降低量，$E$ 是表面电场强度
                                </p>
                            </div>
                            
                            <p>
                                在实际实验中，我们通过测量不同电压下的电流，可以分析Schottky效应的影响。
                                电流与电压的平方根之间存在指数关系：
                            </p>
                            
                            <div class="equation-box">
                                $$\log I = \log I_0 + \beta\sqrt{U}$$
                                <p class="mt-2 mb-0">其中 $\beta$ 是与材料和几何结构相关的常数</p>
                            </div>
                        </div>

                        <!-- 数据处理方法 -->
                        <div class="theory-card">
                            <h2><i class="fas fa-chart-line me-2"></i>数据处理方法</h2>
                            <p>
                                为了从实验数据中提取电子逸出功，我们采用以下数据处理方法：
                            </p>
                            
                            <h5>1. I-U特性曲线分析</h5>
                            <p>
                                绘制不同温度下的电流-电压特性曲线，观察电流随电压的变化规律。
                            </p>
                            
                            <h5>2. Schottky效应分析</h5>
                            <p>
                                对于每个温度，绘制 $\log I$ 与 $\sqrt{U}$ 的关系图，验证Schottky效应的线性关系。
                            </p>
                            
                            <h5>3. 逸出功计算</h5>
                            <p>
                                选择固定电压，绘制 $\log(I/T^2)$ 与 $1/T$ 的关系图，通过线性拟合的斜率计算逸出功：
                            </p>
                            
                            <div class="equation-box">
                                $$\phi = -k \cdot \ln(10) \cdot \text{斜率}$$
                                <p class="mt-2 mb-0">其中斜率来自 $\log(I/T^2)$ 对 $1/T$ 的线性拟合</p>
                            </div>
                        </div>

                        <div class="highlight-box">
                            <h4><i class="fas fa-lightbulb me-2"></i>实验要点</h4>
                            <ul class="mb-0">
                                <li>确保灯丝温度稳定，避免温度波动影响测量精度</li>
                                <li>测量时要等待电流稳定后再记录数据</li>
                                <li>注意电压范围的选择，避免过高电压导致其他效应</li>
                                <li>多次测量取平均值，减少随机误差</li>
                            </ul>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url_for('steps') }}" class="btn btn-custom me-3">
                                <i class="fas fa-arrow-right me-2"></i>查看实验步骤
                            </a>
                            <a href="{{ url_for('input_data', step=1) }}" class="btn btn-custom">
                                <i class="fas fa-play me-2"></i>开始实验
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
